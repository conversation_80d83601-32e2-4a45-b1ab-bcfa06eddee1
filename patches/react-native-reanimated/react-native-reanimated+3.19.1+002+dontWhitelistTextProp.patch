diff --git a/node_modules/react-native-reanimated/src/component/PerformanceMonitor.tsx b/node_modules/react-native-reanimated/src/component/PerformanceMonitor.tsx
index d4b31f2..ced6561 100644
--- a/node_modules/react-native-reanimated/src/component/PerformanceMonitor.tsx
+++ b/node_modules/react-native-reanimated/src/component/PerformanceMonitor.tsx
@@ -46,7 +46,6 @@ function createCircularDoublesBuffer(size: number) {
 }
 
 const DEFAULT_BUFFER_SIZE = 20;
-addWhitelistedNativeProps({ text: true });
 const AnimatedTextInput = createAnimatedComponent(TextInput);
 
 function loopAnimationFrame(fn: (lastTime: number, time: number) => void) {
