---
title: Travel Receipt Integrations
description: Learn how to use pre-built or custom integrations to automatically track travel expenses in Expensify.
keywords: [Expensify Classic, travel receipts, integrations, Bolt Work, SpotHero, Grab, KAYAK, email receipts]
---

<div id="expensify-classic" markdown="1">

Expensify’s receipt integrations allow merchants to automatically send receipts directly to a member’s Expensify account. When a merchant emails a receipt to a member and C<PERSON>'s **<EMAIL>**, Expensify creates a transaction in the account of the email recipient.

You can integrate receipts using Expensify’s pre-built integrations or by setting up a custom receipt integration.

---

# Use a Pre-Built Travel Integration

Expensify offers pre-built integrations with **Bolt Work, SpotHero, Grab, and KAYAK for Business** to automate travel receipt imports.

## Bolt Work

1. Open the **Bolt** app and tap the **menu icon** in the top left.
2. Tap **Work trips** > **Create profile**.
3. Enter the email associated with Expensify, then tap **Next**.
4. Enter your company details and tap **Next**.
5. Select a payment method or tap **Add Payment Method** to add a new one. Then tap **Next**.
6. Tap **Done**.
7. Tap **Add expense provider** > **Expensify**.
8. Tap **Verify**.
9. Return to **Work trips** from the menu.
10. Tap **Add expense provider** and select **Expensify** again.

**Important:** When booking a trip with Bolt Work, select your **work trip profile** as the payment method. This ensures the receipt is automatically sent to Expensify.

---

## SpotHero

1. Open the **SpotHero** app and tap the **menu icon** in the top left.
2. Tap **Account Settings** > **Set up Business Profile**.
3. Tap **Create Business Profile**.
4. Enter the email associated with Expensify and tap **Next**.
5. Tap **Add a Payment Method**, enter payment details, and tap **Next**.
6. Select **Expensify**.

**Tip:** When reserving parking, select your **business profile** in the **Payment Details** section. You can also schedule **weekly or monthly** batch expense submissions in **Business Profile settings**.

---

## Grab

1. Open the **Grab** app and tap your **profile picture** in the top left.
2. Tap your **user icon** at the top of the settings menu.
3. Tap **Add a business profile**.
4. Tap **Next** twice, then **Let’s Get Started**.
5. Enter your Expensify email and tap the **next arrow**.
6. Check your email for a Grab verification code.
7. Go to **Manage My Business Profile**.
8. Under **Preferences**, tap **Expense Solution**.
9. Select **Expensify**, then tap **Save**.

**Note:** When booking a trip, select **business** instead of **personal** to ensure the receipt is sent to Expensify.

---

## Delta for Business

1. Log in to your [Delta Business](https://businessmanagement.delta.com/agentlogin.html) Admin account.
2. Go to Settings > Expense Providers, select Expensify, and follow the instructions listed on page.
3. Once the connection is established, the status for Expensify on the Expense Providers page will change from Pending to Connected, and all incoming data from Delta Business will be routed to Expensify.  

---

# KAYAK for Business

## Admin Setup  
This must be completed by a **KAYAK for Business admin**:

1. Go to the **KAYAK for Business homepage**.
2. Click **Company Settings**.
3. Click **Connect to Expensify**.

Now, all bookings made by employees will automatically sync with Expensify.

## Traveler Setup  

1. On the **KAYAK for Business homepage**, go to **Profile Account Settings**.
2. Enable the **Expensify toggle** to automatically send expenses to Expensify. You can also opt for manual submission.

---

# Build Your Own Receipt Integration  

1. Email **<EMAIL>** with the following details:
   - **Subject:** "Receipt Integration Request"
   - **Body:** A list of email addresses from which the merchant sends receipts.
2. Within **two weeks**, you'll receive confirmation that the email addresses are whitelisted.
3. Once confirmed, Cc **<EMAIL>** when sending receipt emails to members.
4. Test the integration:
   - Send a receipt email to your Expensify email and Cc **<EMAIL>**.
   - Wait for SmartScan to process it.
   - Check that the merchant, date, and amount are correctly added to the transaction.

---

# Using the Integration  

When emailing a receipt:

- **Attach the receipt** as the only attachment (unless it's an .ics file).
- **Include only one email address** in the **To** field.
- **Cc only** `<EMAIL>`.
- **Hotel and car rental reservations** cannot be sent as expenses because payment occurs at checkout.
- **Use standardized currency codes** (ISO 4217) where applicable.

---

# FAQ  

## Why don’t I see the “Send to Expensify” option in Trainline?  

This may occur if the native iOS Mail app is not installed. You can still use the **Share to Expensify** function in iOS for Trainline receipts.

## Why does it take two weeks to set up a custom integration?  

Receipt integrations require **manual setup by Expensify engineers**, which takes up to two weeks.

## Can I connect via API instead of email?  

No, Expensify does not currently offer API-based receipt integrations. All integrations must be set up via email.

## What is Expensify’s Open API used for?  

Expensify’s Open API is designed for exporting data to external accounting systems. Receipt imports must be done via email integration.

## Can Expensify split one email into multiple receipts?  

No, but after SmartScan processes the receipt, you can **[split the expense](https://help.expensify.com/articles/expensify-classic/expenses/Split-an-expense)** manually.

## Can we set up a co-marketing partnership with Expensify?  

No, Expensify does not offer co-marketing partnerships at this time.

## Can we promote our custom Expensify integration?  

Yes! You can promote your integration on your **website** and **social media** (tag **@Expensify** and use the **#Expensify** hashtag). Consider adding:
- A brief **overview** of how the integration works.
- Key **benefits** of using it.
- A **setup guide**.
- Contact details for **integration support**.

## How can I get help?  

For support, contact **Concierge** by clicking the **green chat icon** in the mobile or web app, or email **<EMAIL>**. Our global team is ready to assist you.

</div>
