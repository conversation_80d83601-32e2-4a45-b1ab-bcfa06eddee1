name: Post Deploy Comments

on:
  workflow_call:
    inputs:
      version:
        description: The version that was deployed
        required: true
        type: string
      env:
        description: The environment that was deployed (staging or prod)
        required: true
        type: string
      android:
        description: Android HybridApp deploy status
        required: true
        type: string
      ios:
        description: iOS HybridApp deploy status
        required: true
        type: string
      web:
        description: Web deploy status
        required: true
        type: string
      desktop:
        description: Desktop deploy status
        required: true
        type: string
  workflow_dispatch:
    inputs:
      version:
        description: The version that was deployed
        required: true
        type: string
      env:
        description: The environment that was deployed (staging or prod)
        required: true
        type: choice
        options:
          - staging
          - production
      android:
        description: Android HybridApp deploy status
        required: true
        type: choice
        options:
          - success
          - failure
          - cancelled
          - skipped
      ios:
        description: iOS HybridApp deploy status
        required: true
        type: choice
        options:
          - success
          - failure
          - cancelled
          - skipped
      web:
        description: Web deploy status
        required: true
        type: choice
        options:
          - success
          - failure
          - cancelled
          - skipped
      desktop:
        description: Desktop deploy status
        required: true
        type: choice
        options:
          - success
          - failure
          - cancelled
          - skipped
      date:
        description: The date when this deploy occurred
        required: false
        type: string
      note:
        description: Any additional note you want to include with the deploy comment
        required: false
        type: string

jobs:
  postDeployComments:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608

      - name: Setup Node
        uses: ./.github/actions/composite/setupNode

      - name: Get pull request list
        id: getPullRequestList
        uses: ./.github/actions/javascript/getDeployPullRequestList
        with:
          # Staging tags use the `-staging` suffix, production tags use the version only
          TAG: ${{ inputs.version }}${{ inputs.env == 'staging' && '-staging' || '' }}
          GITHUB_TOKEN: ${{ github.token }}
          IS_PRODUCTION_DEPLOY: ${{ inputs.env == 'production' }}

      - name: Comment on issues
        uses: ./.github/actions/javascript/markPullRequestsAsDeployed
        with:
          PR_LIST: ${{ steps.getPullRequestList.outputs.PR_LIST }}
          MOBILE_EXPENSIFY_PR_LIST: ${{ steps.getPullRequestList.outputs.MOBILE_EXPENSIFY_PR_LIST }}
          IS_PRODUCTION_DEPLOY: ${{ inputs.env == 'production' }}
          DEPLOY_VERSION: ${{ inputs.version }}
          GITHUB_TOKEN: ${{ github.token }}
          ANDROID: ${{ inputs.android }}
          DESKTOP: ${{ inputs.desktop }}
          IOS: ${{ inputs.ios }}
          WEB: ${{ inputs.web }}
          DATE: ${{ inputs.date }}
          NOTE: ${{ inputs.note }}
