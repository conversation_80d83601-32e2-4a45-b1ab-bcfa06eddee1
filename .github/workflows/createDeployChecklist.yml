name: Create or update deploy checklist

on:
  workflow_call:
  workflow_dispatch:

jobs:
  createChecklist:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608

      - name: Setup Node
        uses: ./.github/actions/composite/setupNode

      - name: Create or update deploy checklist
        uses: ./.github/actions/javascript/createOrUpdateStagingDeploy
        with:
          GITHUB_TOKEN: ${{ secrets.OS_BOTIFY_TOKEN }}
