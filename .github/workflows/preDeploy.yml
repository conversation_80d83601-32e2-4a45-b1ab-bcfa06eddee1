# Reminder: If this workflow's name changes, update the name in the dependent workflow at .github/workflows/failureNotifier.yml.
name: Process new code merged to main

on:
  push:
    branches: [main]
    paths-ignore: [docs/**, help/**, contributingGuides/**, jest/**, tests/**]

jobs:
  typecheck:
    uses: ./.github/workflows/typecheck.yml

  lint:
    uses: ./.github/workflows/lint.yml

  prettier:
    uses: ./.github/workflows/prettier.yml

  test:
    uses: ./.github/workflows/test.yml

  confirmPassingBuild:
    runs-on: ubuntu-latest
    needs: [typecheck, lint, test]
    if: ${{ always() }}

    steps:
      - uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608 # v4

      - name: Exit failed workflow
        if: ${{ needs.typecheck.result == 'failure' || needs.lint.result == 'failure' || needs.test.result == 'failure' }}
        run: |
          echo "Checks failed, exiting ~ typecheck: ${{ needs.typecheck.result }}, lint: ${{ needs.lint.result }}, test: ${{ needs.test.result }}"
          exit 1

  chooseDeployActions:
    runs-on: ubuntu-latest
    needs: confirmPassingBuild
    outputs:
      MERGED_PR: ${{ steps.getMergedPullRequest.outputs.number }}
      SHOULD_DEPLOY: ${{ fromJSON(steps.shouldDeploy.outputs.SHOULD_DEPLOY) }}

    steps:
      - uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608 # v4

      - name: Get merged pull request
        id: getMergedPullRequest
        uses: actions-ecosystem/action-get-merged-pull-request@59afe90821bb0b555082ce8ff1e36b03f91553d9
        with:
          github_token: ${{ github.token }}

      - name: Check if StagingDeployCash is locked
        id: isStagingDeployLocked
        uses: ./.github/actions/javascript/isStagingDeployLocked
        with:
          GITHUB_TOKEN: ${{ github.token }}

      - name: Check if merged pull request should trigger a deploy
        id: shouldDeploy
        run: echo "SHOULD_DEPLOY=${{ (!fromJSON(steps.isStagingDeployLocked.outputs.IS_LOCKED) && github.actor != 'OSBotify') }}" >> "$GITHUB_OUTPUT"

  skipDeploy:
    runs-on: ubuntu-latest
    needs: chooseDeployActions
    if: ${{ !fromJSON(needs.chooseDeployActions.outputs.SHOULD_DEPLOY) && github.actor != 'OSBotify' }}
    steps:
      - name: Comment on deferred PR
        uses: actions-ecosystem/action-create-comment@cd098164398331c50e7dfdd0dfa1b564a1873fac
        with:
          github_token: ${{ secrets.OS_BOTIFY_TOKEN }}
          number: ${{ needs.chooseDeployActions.outputs.MERGED_PR }}
          body: |
            :hand: This PR was not deployed to staging yet because QA is ongoing. It will be automatically deployed to staging after the next production release.

  createNewVersion:
    needs: chooseDeployActions
    if: ${{ fromJSON(needs.chooseDeployActions.outputs.SHOULD_DEPLOY) }}
    uses: ./.github/workflows/createNewVersion.yml
    secrets: inherit

  updateStaging:
    needs: [chooseDeployActions, createNewVersion]
    uses: ./.github/workflows/updateProtectedBranch.yml
    secrets: inherit
    with:
      TARGET_BRANCH: staging
