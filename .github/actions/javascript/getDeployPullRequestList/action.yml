name: 'Get Release Pull Request List'
description: 'Gather all the PRs being deployed for given release tag.'
inputs:
    TAG:
        description: Git tag
        required: true
    GITHUB_TOKEN:
        description: "Github token for authentication"
        required: true
    IS_PRODUCTION_DEPLOY:
        description: "True if we are deploying to production"
        required: false
outputs:
    PR_LIST:
        description: Array of pull request numbers
    MOBILE_EXPENSIFY_PR_LIST:
        description: Array of Mobile-Expensify pull request numbers
runs:
    using: 'node20'
    main: './index.js'
