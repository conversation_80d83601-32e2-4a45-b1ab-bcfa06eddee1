name: "<PERSON>ull Requests as Deployed"
description: "Mark pull requests as deployed on production or staging"
inputs:
    PR_LIST:
        description: "Array of pull request numbers"
        required: true
    MOBILE_EXPENSIFY_PR_LIST:
        description: "Array of Mobile-Expensify pull request numbers"
        required: false
    IS_PRODUCTION_DEPLOY:
        description: "Check if deploying to production"
        required: false
        default: "false"
    DEPLOY_VERSION:
        description: "The app version in which the pull requests were deployed"
        required: true
    GITHUB_TOKEN:
        description: "Github token for authentication"
        required: true
    ANDROID:
        description: "Android job result ('success', 'failure', 'cancelled', or 'skipped')"
        required: true
    DESKTOP:
        description: "Desktop job result ('success', 'failure', 'cancelled', or 'skipped')"
        required: true
    IOS:
        description: "iOS job result ('success', 'failure', 'cancelled', or 'skipped')"
        required: true
    WEB:
        description: "Web job result ('success', 'failure', 'cancelled', or 'skipped')"
        required: true
    DATE:
        description: "The date of deployment"
        required: false
    NOTE:
        description: "Additional note from the deployer"
        required: false
runs:
    using: "node20"
    main: "./index.js"
