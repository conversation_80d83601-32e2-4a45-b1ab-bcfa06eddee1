/* eslint-disable @typescript-eslint/naming-convention */
// we need "dirty" object key names in these tests
import {generatePolicyID} from '@libs/actions/Policy/Policy';
import CONST from '@src/CONST';
import {
    buildFilterFormValuesFromQuery,
    buildQueryStringFromFilterFormValues,
    buildSearchQueryJSON,
    getQueryWithUpdatedValues,
    shouldHighlight,
    sortOptionsWithEmptyValue,
} from '@src/libs/SearchQueryUtils';
import ONYXKEYS from '@src/ONYXKEYS';
import type {SearchAdvancedFiltersForm} from '@src/types/form';
import {localeCompare} from '../../utils/TestHelper';

const personalDetailsFakeData = {
    '<EMAIL>': {
        accountID: 12345,
    },
    '<EMAIL>': {
        accountID: 78901,
    },
} as Record<string, {accountID: number}>;

jest.mock('@libs/PersonalDetailsUtils', () => {
    return {
        getPersonalDetailByEmail(email: string) {
            return personalDetailsFakeData[email];
        },
    };
});

// The default query is generated by default values from parser, which are defined in grammar.
// We don't want to test or mock the grammar and the parser, so we're simply defining this string directly here.
const defaultQuery = `type:expense sortBy:date sortOrder:desc`;

describe('SearchQueryUtils', () => {
    describe('getQueryWithUpdatedValues', () => {
        test('returns default query for empty value', () => {
            const userQuery = '';

            const result = getQueryWithUpdatedValues(userQuery);

            expect(result).toEqual(defaultQuery);
        });

        test('returns query with updated amounts', () => {
            const userQuery = 'foo test amount:20000';

            const result = getQueryWithUpdatedValues(userQuery);

            expect(result).toEqual(`${defaultQuery} amount:2000000 foo test`);
        });

        test('returns query with user emails substituted', () => {
            const userQuery = 'from:<EMAIL> hello';

            const result = getQueryWithUpdatedValues(userQuery);

            expect(result).toEqual(`${defaultQuery} from:12345 hello`);
        });

        test('returns query with user emails substituted and preserves user ids', () => {
            const userQuery = 'from:<EMAIL> to:112233';

            const result = getQueryWithUpdatedValues(userQuery);

            expect(result).toEqual(`${defaultQuery} from:12345 to:112233`);
        });

        test('returns query with all of the fields correctly substituted', () => {
            const userQuery = 'from:9876,87654 to:<EMAIL> hello amount:150 test';

            const result = getQueryWithUpdatedValues(userQuery);

            expect(result).toEqual(`${defaultQuery} from:9876,87654 to:78901 amount:15000 hello test`);
        });

        test('returns query with updated groupBy', () => {
            const userQuery = 'from:<EMAIL> groupBy:reports';

            const result = getQueryWithUpdatedValues(userQuery);

            expect(result).toEqual(`${defaultQuery} groupBy:reports from:12345`);
        });
    });

    describe('buildQueryStringFromFilterFormValues', () => {
        test('simple filter value', () => {
            const filterValues: Partial<SearchAdvancedFiltersForm> = {
                type: 'expense',
                status: CONST.SEARCH.STATUS.EXPENSE.ALL,
                policyID: ['12345'],
                lessThan: '100',
            };

            const result = buildQueryStringFromFilterFormValues(filterValues);

            expect(result).toEqual('sortBy:date sortOrder:desc type:expense policyID:12345 amount<100');
        });

        test('with Policy ID', () => {
            const filterValues: Partial<SearchAdvancedFiltersForm> = {
                policyID: ['12345'],
            };

            const result = buildQueryStringFromFilterFormValues(filterValues);

            expect(result).toEqual('sortBy:date sortOrder:desc policyID:12345');
        });

        test('with keywords', () => {
            const filterValues: Partial<SearchAdvancedFiltersForm> = {
                type: 'expense',
                status: CONST.SEARCH.STATUS.EXPENSE.ALL,
                policyID: ['67890'],
                merchant: 'Amazon',
                description: 'Electronics',
                keyword: 'laptop',
                category: ['electronics', 'gadgets'],
            };

            const result = buildQueryStringFromFilterFormValues(filterValues);

            expect(result).toEqual('sortBy:date sortOrder:desc type:expense policyID:67890 merchant:Amazon description:Electronics laptop category:electronics,gadgets');
        });

        test('currencies and categories', () => {
            const filterValues: Partial<SearchAdvancedFiltersForm> = {
                type: 'expense',
                status: CONST.SEARCH.STATUS.EXPENSE.ALL,
                category: ['services', 'consulting'],
                currency: ['USD', 'EUR'],
            };

            const result = buildQueryStringFromFilterFormValues(filterValues);

            expect(result).toEqual('sortBy:date sortOrder:desc type:expense category:services,consulting currency:USD,EUR');
        });

        test('has empty category values', () => {
            const filterValues: Partial<SearchAdvancedFiltersForm> = {
                type: 'expense',
                status: CONST.SEARCH.STATUS.EXPENSE.ALL,
                category: ['equipment', 'consulting', 'none,Uncategorized'],
            };

            const result = buildQueryStringFromFilterFormValues(filterValues);

            expect(result).toEqual('sortBy:date sortOrder:desc type:expense category:equipment,consulting,none,Uncategorized');
        });

        test('empty filter values', () => {
            const filterValues: Partial<SearchAdvancedFiltersForm> = {};

            const result = buildQueryStringFromFilterFormValues(filterValues);

            expect(result).toEqual('sortBy:date sortOrder:desc');
        });

        test('array of from', () => {
            const filterValues: Partial<SearchAdvancedFiltersForm> = {
                type: 'expense',
                from: ['<EMAIL>', '<EMAIL>'],
                to: ['<EMAIL>'],
            };
            const result = buildQueryStringFromFilterFormValues(filterValues);

            expect(result).toEqual('sortBy:date sortOrder:desc type:expense from:<EMAIL>,<EMAIL> to:<EMAIL>');
        });

        test('complex filter values', () => {
            const filterValues: Partial<SearchAdvancedFiltersForm> = {
                type: 'expense',
                from: ['<EMAIL>', '<EMAIL>'],
                to: ['<EMAIL>'],
                dateAfter: '2025-03-01',
                dateBefore: '2025-03-10',
                lessThan: '1000',
                greaterThan: '1',
                category: ['finance', 'insurance'],
            };
            const result = buildQueryStringFromFilterFormValues(filterValues);

            expect(result).toEqual(
                'sortBy:date sortOrder:desc type:expense from:<EMAIL>,<EMAIL> to:<EMAIL> category:finance,insurance date>2025-03-01 date<2025-03-10 amount>1 amount<1000',
            );
            expect(result).not.toMatch(CONST.VALIDATE_FOR_HTML_TAG_REGEX);
        });

        test('with withdrawal type filter', () => {
            const filterValues: Partial<SearchAdvancedFiltersForm> = {
                type: 'expense',
                status: CONST.SEARCH.STATUS.EXPENSE.ALL,
                withdrawalType: CONST.SEARCH.WITHDRAWAL_TYPE.EXPENSIFY_CARD,
            };

            const result = buildQueryStringFromFilterFormValues(filterValues);

            expect(result).toEqual('sortBy:date sortOrder:desc type:expense withdrawalType:expensify-card');
        });

        test('with withdrawn filter', () => {
            const filterValues: Partial<SearchAdvancedFiltersForm> = {
                type: 'expense',
                status: CONST.SEARCH.STATUS.EXPENSE.ALL,
                withdrawnOn: CONST.SEARCH.DATE_PRESETS.LAST_MONTH,
            };

            const result = buildQueryStringFromFilterFormValues(filterValues);

            expect(result).toEqual('sortBy:date sortOrder:desc type:expense withdrawn:last-month');
        });
    });

    describe('buildFilterFormValuesFromQuery', () => {
        test('category filter includes empty values', () => {
            const policyID = generatePolicyID();
            const queryString = 'sortBy:date sortOrder:desc type:expense category:none,Uncategorized,Maintenance';
            const queryJSON = buildSearchQueryJSON(queryString);

            const policyCategories = {
                [`${ONYXKEYS.COLLECTION.POLICY_CATEGORIES}${policyID}`]: {
                    Maintenance: {
                        enabled: true,
                        name: 'Maintenance',
                    },
                    Travel: {
                        enabled: true,
                        name: 'Travel',
                    },
                    Meals: {
                        enabled: true,
                        name: 'Meals',
                    },
                },
            };
            const policyTags = {};
            const currencyList = {};
            const personalDetails = {};
            const cardList = {};
            const reports = {};
            const taxRates = {};

            if (!queryJSON) {
                throw new Error('Failed to parse query string');
            }

            const result = buildFilterFormValuesFromQuery(queryJSON, policyCategories, policyTags, currencyList, personalDetails, cardList, reports, taxRates);

            expect(result).toEqual({
                type: 'expense',
                status: CONST.SEARCH.STATUS.EXPENSE.ALL,
                category: ['Maintenance', 'none,Uncategorized'],
            });
        });
    });

    describe('shouldHighlight', () => {
        it('returns false if either input is empty', () => {
            expect(shouldHighlight('', 'test')).toBe(false);
            expect(shouldHighlight('Some text', '')).toBe(false);
        });

        it('matches exact word at beginning', () => {
            expect(shouldHighlight('Take a 2-minute tour', 'Take')).toBe(true);
        });

        it('matches exact word in middle', () => {
            expect(shouldHighlight('Take a 2-minute tour', '2-minute')).toBe(true);
        });

        it('matches phrase with leading space', () => {
            expect(shouldHighlight('Take a 2-minute tour', ' 2-minute tour')).toBe(true);
        });

        it('matches with special characters', () => {
            // cspell:disable-next-line
            expect(shouldHighlight('Explore the #%tự đặc biệt!', '#%tự')).toBe(true);
        });

        it('is case-insensitive', () => {
            expect(shouldHighlight('Take a 2-minute tour', 'TOUR')).toBe(true);
        });

        it('does not match partial word in the middle', () => {
            expect(shouldHighlight('Take a 2-minute tour', 'in')).toBe(false);
        });

        it('does not match incomplete trailing text', () => {
            expect(shouldHighlight('Take a 2-minute tour', '2-minute to')).toBe(false);
        });

        it('matches multi-word phrase exactly', () => {
            expect(shouldHighlight('Take a 2-minute tour', '2-minute tour')).toBe(true);
        });

        it('does not match words out of order', () => {
            expect(shouldHighlight('Take a 2-minute tour', 'tour 2-minute')).toBe(false);
        });
    });

    describe('sortOptionsWithEmptyValue', () => {
        it('should prioritize empty values at the start', () => {
            const options = ['B', 'A', CONST.SEARCH.CATEGORY_EMPTY_VALUE, 'C'];
            const sortedOptions = options.sort((a, b) => sortOptionsWithEmptyValue(a, b, localeCompare));

            expect(sortedOptions).toEqual([CONST.SEARCH.CATEGORY_EMPTY_VALUE, 'A', 'B', 'C']);
        });

        it('should sort non-empty values properly', () => {
            const options = ['B', 'A', 'C'];
            const sortedOptions = options.sort((a, b) => sortOptionsWithEmptyValue(a, b, localeCompare));

            expect(sortedOptions).toEqual(['A', 'B', 'C']);
        });
    });
});
